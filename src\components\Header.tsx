import { Button } from "@/components/ui/button";
import { Menu, X, ChevronDown } from "lucide-react";
import { useState, useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { useLocation, Link } from "react-router-dom";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isServicesOpen, setIsServicesOpen] = useState(false);
  const [isMobileServicesOpen, setIsMobileServicesOpen] = useState(false);
  const isMobile = useIsMobile();
  const location = useLocation();

  const navigationItems = [
    { label: "Home", href: "/", path: "/" },
    { label: "Point of Sale", href: "https://zykapos.eriasoftware.com", path: "/point-of-sale", external: true },
    { label: "Inventory", href: "/inventory", path: "/inventory" },
    { label: "About Us", href: "/about-us", path: "/about-us" },
    { label: "Contact Us", href: "/contact", path: "/contact" },
  ];

  const servicesItems = [
    { label: "Website Development", href: "/website-development", path: "/website-development" },
    { label: "App Development", href: "/app-development", path: "/app-development" },
    { label: "FreshService CRM", href: "/freshservice-crm", path: "/freshservice-crm" },
  ];

  // Scroll detection effect
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 10);
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (isServicesOpen && !(event.target as Element).closest('.services-dropdown')) {
        setIsServicesOpen(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    document.addEventListener('click', handleClickOutside);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [isServicesOpen]);

  const handleNavClick = () => {
    setIsMenuOpen(false);
    setIsMobileServicesOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    setIsMobileServicesOpen(false); // Close services dropdown when menu toggles
  };

  const toggleMobileServices = () => {
    setIsMobileServicesOpen(!isMobileServicesOpen);
  };

  // Dynamic header classes based on scroll state and mobile
  const getHeaderClasses = () => {
    const baseClasses = "w-full sticky top-0 z-50 transition-all duration-300";
    const borderStyle = "border-b border-[#e6e6e6]";

    if (isMobile && (isScrolled || isMenuOpen)) {
      return `${baseClasses} ${borderStyle} bg-gray-900 backdrop-blur-md`;
    }

    return `${baseClasses} ${borderStyle} bg-background`;
  };

  // Dynamic text classes for mobile dark mode
  const getTextClasses = (baseClasses: string) => {
    if (isMobile && (isScrolled || isMenuOpen)) {
      return `${baseClasses} text-white`;
    }
    return baseClasses;
  };

  return (
    <header className={getHeaderClasses()}>
      <div className="container mx-auto px-6 lg:px-8 flex items-center justify-between" style={{paddingTop: '22px', paddingBottom: '22px'}}>
        {/* Logo */}
        <div className="flex items-center">
          <Link to="/" className="group">
            <h1 className="flex items-baseline space-x-1">
              <span className={getTextClasses("text-2xl md:text-3xl font-bold text-blue-600 tracking-tight hover:text-blue-700 transition-colors")}>
                ERIA
              </span>
              <span className={getTextClasses("text-2xl md:text-3xl font-normal text-gray-600 tracking-tight hover:text-gray-700 transition-colors")}>
                Software
              </span>
            </h1>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          {navigationItems.slice(0, 3).map((item) => (
            item.external ? (
              <a
                key={item.label}
                href={item.href}
                target="_blank"
                rel="noopener noreferrer"
                className={getTextClasses(
                  `text-base font-medium transition-colors ${
                    location.pathname === item.path
                      ? 'text-blue-600 font-semibold'
                      : 'text-foreground hover:text-blue-600'
                  }`
                )}
              >
                {item.label}
              </a>
            ) : (
              <Link
                key={item.label}
                to={item.href}
                className={getTextClasses(
                  `text-base font-medium transition-colors ${
                    location.pathname === item.path
                      ? 'text-blue-600 font-semibold'
                      : 'text-foreground hover:text-blue-600'
                  }`
                )}
              >
                {item.label}
              </Link>
            )
          ))}

          {/* Services Dropdown */}
          <div className="relative services-dropdown">
            <button
              onClick={() => setIsServicesOpen(!isServicesOpen)}
              className={getTextClasses(
                `text-base font-medium transition-colors flex items-center gap-1 ${
                  servicesItems.some(item => location.pathname === item.path)
                    ? 'text-blue-600 font-semibold'
                    : 'text-foreground hover:text-blue-600'
                }`
              )}
            >
              Services
              <ChevronDown className={`w-4 h-4 transition-transform ${isServicesOpen ? 'rotate-180' : ''}`} />
            </button>

            {isServicesOpen && (
              <div className="absolute top-full left-0 mt-2 w-56 bg-white rounded-md shadow-lg border border-gray-200 py-2 z-50">
                {servicesItems.map((item) => (
                  <Link
                    key={item.label}
                    to={item.href}
                    onClick={() => setIsServicesOpen(false)}
                    className={`block px-4 py-2 text-sm transition-colors ${
                      location.pathname === item.path
                        ? 'text-blue-600 bg-blue-50 font-medium'
                        : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                    }`}
                  >
                    {item.label}
                  </Link>
                ))}
              </div>
            )}
          </div>

          {navigationItems.slice(3).map((item) => (
            <Link
              key={item.label}
              to={item.href}
              className={getTextClasses(
                `text-base font-medium transition-colors ${
                  location.pathname === item.path
                    ? 'text-blue-600 font-semibold'
                    : 'text-foreground hover:text-blue-600'
                }`
              )}
            >
              {item.label}
            </Link>
          ))}
        </nav>

        {/* Marathi Language Button */}
        <div className="hidden md:flex items-center ml-4">
          <button
            onClick={() => window.open('https://marathi.eriasoftware.com/', '_blank')}
            className={getTextClasses(
              "flex items-center justify-center w-10 h-10 rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold text-lg shadow-md hover:shadow-lg transition-all duration-300 group"
            )}
            title="View website in Marathi"
          >
            <span className="group-hover:scale-110 transition-transform duration-200">म</span>
          </button>
        </div>

        {/* Desktop CTA Button */}
        <div className="hidden md:block">
          <Button
            variant="hero"
            size="default"
            className="text-sm px-6 py-2 bg-blue-600 text-white hover:bg-blue-700"
            onClick={() => window.open('https://zykapos.eriasoftware.com', '_blank')}
          >
            Get a Demo
          </Button>
        </div>

        {/* Mobile Hamburger Button */}
        <Button
          variant="ghost"
          size="default"
          onClick={toggleMenu}
          className={`md:hidden p-2 ${isMobile && (isScrolled || isMenuOpen) ? 'text-white hover:bg-white/10' : 'hover:bg-accent'}`}
        >
          {isMenuOpen ? (
            <X className={`h-6 w-6 ${isMobile && (isScrolled || isMenuOpen) ? 'text-white' : ''}`} />
          ) : (
            <Menu className={`h-6 w-6 ${isMobile && (isScrolled || isMenuOpen) ? 'text-white' : ''}`} />
          )}
          <span className="sr-only">Toggle navigation menu</span>
        </Button>
      </div>

      {/* Mobile Expanded Navigation */}
      {isMobile && isMenuOpen && (
        <div className="md:hidden bg-gray-900 border-t border-gray-700">
          <nav className="container mx-auto px-6 lg:px-8 py-4">
            <div className="flex flex-col space-y-3">
              {navigationItems.slice(0, 3).map((item) => (
                item.external ? (
                  <a
                    key={item.label}
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={handleNavClick}
                    className={`transition-colors py-3 px-4 rounded-md text-base font-medium ${
                      location.pathname === item.path
                        ? 'text-blue-400 bg-gray-800 font-semibold'
                        : 'text-white hover:text-gray-300 hover:bg-gray-800'
                    }`}
                  >
                    {item.label}
                  </a>
                ) : (
                  <Link
                    key={item.label}
                    to={item.href}
                    onClick={handleNavClick}
                    className={`transition-colors py-3 px-4 rounded-md text-base font-medium ${
                      location.pathname === item.path
                        ? 'text-blue-400 bg-gray-800 font-semibold'
                        : 'text-white hover:text-gray-300 hover:bg-gray-800'
                    }`}
                  >
                    {item.label}
                  </Link>
                )
              ))}

              {/* Services Section with Collapsible Dropdown */}
              <div className="py-2">
                <button
                  onClick={toggleMobileServices}
                  className={`w-full flex items-center justify-between transition-colors py-3 px-4 rounded-md text-base font-medium ${
                    servicesItems.some(item => location.pathname === item.path)
                      ? 'text-blue-400 bg-gray-800 font-semibold'
                      : 'text-white hover:text-gray-300 hover:bg-gray-800'
                  }`}
                >
                  <span>Services</span>
                  <ChevronDown className={`w-4 h-4 transition-transform ${isMobileServicesOpen ? 'rotate-180' : ''}`} />
                </button>

                {/* Collapsible Services Dropdown */}
                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isMobileServicesOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}>
                  <div className="pt-2 pb-1">
                    {servicesItems.map((item) => (
                      <Link
                        key={item.label}
                        to={item.href}
                        onClick={handleNavClick}
                        className={`transition-colors py-2 px-8 rounded-md text-sm font-medium block ${
                          location.pathname === item.path
                            ? 'text-blue-400 bg-gray-800 font-semibold'
                            : 'text-gray-300 hover:text-white hover:bg-gray-800'
                        }`}
                      >
                        {item.label}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>

              {navigationItems.slice(3).map((item) => (
                <Link
                  key={item.label}
                  to={item.href}
                  onClick={handleNavClick}
                  className={`transition-colors py-3 px-4 rounded-md text-base font-medium ${
                    location.pathname === item.path
                      ? 'text-blue-400 bg-gray-800 font-semibold'
                      : 'text-white hover:text-gray-300 hover:bg-gray-800'
                  }`}
                >
                  {item.label}
                </Link>
              ))}
              <div className="pt-3 border-t border-gray-700 space-y-3">
                <Button
                  variant="hero"
                  size="default"
                  className="w-full text-sm px-6 py-2 bg-blue-600 text-white hover:bg-blue-700"
                  onClick={() => {
                    handleNavClick();
                    window.open('https://zykapos.eriasoftware.com', '_blank');
                  }}
                >
                  Get a Demo
                </Button>

                <button
                  onClick={() => {
                    handleNavClick();
                    window.open('https://marathi.eriasoftware.com/', '_blank');
                  }}
                  className="w-full flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium transition-all duration-300"
                >
                  <span className="text-lg font-bold">म</span>
                  <span>मराठी वेबसाइट</span>
                </button>
              </div>
            </div>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;